# Comments API Integration

This document describes the comment system integration for the blog posts.

## Overview

The comment system allows users to add comments to blog posts. It includes:

- **Frontend Components**: React components for displaying and submitting comments
- **API Integration**: TypeScript API functions for comment operations
- **State Management**: React Context for managing comment state
- **Form Validation**: Zod schema validation for comment forms

## API Endpoints

The comment system expects the following API endpoints to be available:

### Get Comments for a Blog Post
```
GET /blogs/{blogId}/comments?page=1&limit=10
```

**Response:**
```json
{
  "status": 200,
  "success": true,
  "message": "Comments retrieved successfully",
  "data": [
    {
      "_id": "comment_id",
      "blogId": "blog_id",
      "author": "<PERSON>",
      "comment": "Great article!",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "status": "approved"
    }
  ],
  "pagination": {
    "totalItems": 1,
    "totalPages": 1,
    "currentPage": 1,
    "pageSize": 10,
    "hasNext": false,
    "hasPrev": false
  }
}
```

### Create a New Comment
```
POST /blogs/{blogId}/comments
```

**Request Body:**
```json
{
  "author": "John Doe",
  "comment": "Great article!"
}
```

**Response:**
```json
{
  "status": 201,
  "success": true,
  "message": "Comment created successfully",
  "data": {
    "_id": "comment_id",
    "blogId": "blog_id",
    "author": "John Doe",
    "comment": "Great article!",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "status": "pending"
  }
}
```

## Frontend Components

### Comments Component
Located at: `src/components/landing/blog/Comments.tsx`

This component handles:
- Displaying existing comments
- Comment submission form
- Loading states
- Error handling
- Success messages

### Usage
```tsx
import Comments from "@/components/landing/blog/Comments";

<Comments blogId={post.id} />
```

## Context Provider

### BlogProvider (with Comments)
Located at: `src/contexts/BlogContext.tsx`

The existing BlogProvider has been extended to include comment state management.

### Usage
```tsx
import { BlogProvider } from "@/contexts/BlogContext";

<BlogProvider>
  <YourComponent />
</BlogProvider>
```

## API Functions

Located at: `src/lib/api/blogs.ts`

- `getComments(blogId, page, limit)` - Fetch comments for a blog post
- `createComment(commentData)` - Create a new comment

## Form Validation

Located at: `src/schemas/comment.schema.ts`

Validation rules:
- **Author**: 1-50 characters, required
- **Comment**: 1-1000 characters, required

## Types

Located at: `src/types/blog.ts`

Key types:
- `Comment` - Comment data structure
- `CreateCommentData` - Data for creating comments
- `CommentsResponse` - API response for comment lists
- `SingleCommentResponse` - API response for single comment

## Error Handling

The system includes comprehensive error handling:
- Network errors
- Validation errors
- API errors
- Loading states

## Security Considerations

- Comments may require moderation (status: "pending" | "approved" | "rejected")
- Input validation prevents XSS attacks
- Rate limiting should be implemented on the backend
- Only author name and comment content are collected

## Backend Requirements

The backend should implement:
1. Comment CRUD operations
2. Pagination for comment lists
3. Input validation and sanitization
4. Optional comment moderation system
5. Rate limiting for comment submission
6. Email validation
7. Spam protection (optional)

## Testing

To test the comment system:
1. Navigate to any blog post
2. Scroll to the comments section
3. Fill out the comment form
4. Submit the comment
5. Verify the comment appears in the list (if auto-approved)
6. Check for proper error handling with invalid inputs
