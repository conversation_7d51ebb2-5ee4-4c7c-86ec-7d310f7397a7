import React from "react";
import SingleBlogPost from "@/components/landing/blog/SingleBlogPost";
import { BlogProvider } from "@/contexts/BlogContext";
import { CommentProvider } from "@/contexts/CommentContext";

// Configure Edge Runtime for Cloudflare Pages
export const runtime = "edge";

type BlogPostPageProps = {
  params: {
    slug: string;
  };
};

const BlogPostPage = ({ params }: BlogPostPageProps) => {
  return (
    <BlogProvider>
      <CommentProvider>
        <SingleBlogPost slug={params.slug} />
      </CommentProvider>
    </BlogProvider>
  );
};

export default BlogPostPage;
