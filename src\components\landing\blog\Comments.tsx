"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { commentSchema, type CommentFormData } from "@/schemas/comment.schema";
import { useComments } from "@/contexts/CommentContext";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import LoadingSpinner from "@/components/common/LoadingSpinner";

type CommentsProps = {
  blogId: string;
};

const Comments = ({ blogId }: CommentsProps) => {
  const {
    comments,
    commentsLoading,
    commentsError,
    totalComments,
    fetchComments,
    addComment,
    clearComments,
  } = useComments();

  const [submitting, setSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CommentFormData>({
    resolver: zodResolver(commentSchema),
  });

  // Fetch comments on component mount
  useEffect(() => {
    if (blogId) {
      fetchComments(blogId);
    }

    // Cleanup when component unmounts
    return () => {
      clearComments();
    };
  }, [blogId, fetchComments, clearComments]);

  // Handle form submission
  const onSubmit = async (data: CommentFormData) => {
    setSubmitting(true);
    setSuccessMessage(null);

    try {
      const success = await addComment({
        blogId,
        name: data.name,
        email: data.email,
        message: data.message,
      });

      if (success) {
        setSuccessMessage("Comment submitted successfully! It may take a moment to appear.");
        reset(); // Clear the form

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);
      }
    } catch (err) {
      console.error("Error submitting comment:", err);
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="mx-auto mt-16 max-w-7xl">
      {/* Divider */}
      <div className="mb-10 h-[3px] w-full bg-gray-200" />

      {/* Comments Header */}
      <h2 className="mb-8 text-2xl font-bold text-accent">
        Comments ({totalComments})
      </h2>

      {/* Comments List */}
      <div className="mb-12 space-y-6">
        {commentsLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner width={40} height={40} />
          </div>
        ) : comments.length > 0 ? (
          comments.map((comment) => (
            <div key={comment._id} className="flex space-x-4">
              <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-full bg-gray-200">
                <div className="flex h-full w-full items-center justify-center text-lg font-semibold text-gray-600">
                  {comment.name.charAt(0).toUpperCase()}
                </div>
              </div>
              <div className="flex-1">
                <div className="mb-1 flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-black">{comment.name}</h4>
                    <p className="text-xs text-[#898989]">
                      {formatDate(comment.createdAt)}
                    </p>
                  </div>
                  {comment.status === "pending" && (
                    <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                      Pending Approval
                    </span>
                  )}
                </div>
                <p className="text-sm leading-relaxed text-[#636363]">
                  {comment.message}
                </p>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500 py-8">
            No comments yet. Be the first to comment!
          </p>
        )}
      </div>

      {/* Leave a Reply Section */}
      <div>
        <h3 className="mb-6 text-xl font-bold text-accent">Leave a reply</h3>
        
        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 rounded-lg bg-green-50 border border-green-200 p-4">
            <p className="text-green-800">{successMessage}</p>
          </div>
        )}

        {/* Error Message */}
        {commentsError && (
          <div className="mb-4 rounded-lg bg-red-50 border border-red-200 p-4">
            <p className="text-red-800">{commentsError}</p>
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Message Textarea */}
          <div>
            <textarea
              {...register("message")}
              placeholder="Type a message"
              rows={6}
              className={cn(
                "w-full rounded-lg border px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-1",
                errors.message
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:border-accent focus:ring-accent"
              )}
            />
            {errors.message && (
              <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
            )}
          </div>

          {/* Name Input */}
          <div>
            <input
              {...register("name")}
              type="text"
              placeholder="Name"
              className={cn(
                "w-full rounded-lg border px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-1",
                errors.name
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:border-accent focus:ring-accent"
              )}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Email Input */}
          <div>
            <input
              {...register("email")}
              type="email"
              placeholder="Email"
              className={cn(
                "w-full rounded-lg border px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-1",
                errors.email
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:border-accent focus:ring-accent"
              )}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={submitting}
              className={cn(
                buttonVariants({ variant: "default", size: "lg" }),
                "w-full rounded-lg px-6 py-3 font-medium",
                submitting && "opacity-50 cursor-not-allowed"
              )}
            >
              {submitting ? (
                <div className="flex items-center justify-center gap-2">
                  <LoadingSpinner width={20} height={20} />
                  Submitting...
                </div>
              ) : (
                "Submit"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Comments;
