"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import { getComments, createComment } from "@/lib/api/comments";
import { type Comment, type CreateCommentData } from "@/types/blog";

type CommentContextType = {
  // Comments state
  comments: Comment[];
  commentsLoading: boolean;
  commentsError: string | null;
  totalComments: number;

  // Actions
  fetchComments: (blogId: string, page?: number, limit?: number) => Promise<void>;
  addComment: (commentData: CreateCommentData) => Promise<boolean>;
  clearComments: () => void;
};

const CommentContext = createContext<CommentContextType | undefined>(undefined);

type CommentProviderProps = {
  children: ReactNode;
};

export const CommentProvider = ({ children }: CommentProviderProps) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [commentsError, setCommentsError] = useState<string | null>(null);
  const [totalComments, setTotalComments] = useState(0);

  const fetchComments = useCallback(
    async (blogId: string, page = 1, limit = 10) => {
      setCommentsLoading(true);
      setCommentsError(null);

      try {
        const response = await getComments(blogId, page, limit);

        if (response.success && response.data) {
          setComments(response.data);
          setTotalComments(response.pagination?.totalItems || response.data.length);
        } else {
          setCommentsError(response.message || "Failed to fetch comments");
        }
      } catch (error) {
        setCommentsError("An unexpected error occurred");
        console.error("Error fetching comments:", error);
      } finally {
        setCommentsLoading(false);
      }
    },
    [],
  );

  const addComment = useCallback(
    async (commentData: CreateCommentData): Promise<boolean> => {
      try {
        const response = await createComment(commentData);

        if (response.success && response.data) {
          // Add the new comment to the beginning of the list
          setComments((prev) => [response.data, ...prev]);
          setTotalComments((prev) => prev + 1);
          return true;
        } else {
          setCommentsError(response.message || "Failed to add comment");
          return false;
        }
      } catch (error) {
        setCommentsError("An unexpected error occurred while adding comment");
        console.error("Error adding comment:", error);
        return false;
      }
    },
    [],
  );

  const clearComments = useCallback(() => {
    setComments([]);
    setCommentsError(null);
    setTotalComments(0);
  }, []);

  const value: CommentContextType = {
    comments,
    commentsLoading,
    commentsError,
    totalComments,
    fetchComments,
    addComment,
    clearComments,
  };

  return (
    <CommentContext.Provider value={value}>{children}</CommentContext.Provider>
  );
};

export const useComments = (): CommentContextType => {
  const context = useContext(CommentContext);
  if (context === undefined) {
    throw new Error("useComments must be used within a CommentProvider");
  }
  return context;
};
