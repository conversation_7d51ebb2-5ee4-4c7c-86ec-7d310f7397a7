// Example test file for comment API functions
// Note: This is a basic example. You'll need to set up a proper testing framework.

import { getComments, createComment } from '../comments';

// Mock the api module
jest.mock('../index', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));

describe('Comments API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getComments', () => {
    it('should fetch comments successfully', async () => {
      const mockResponse = {
        data: {
          status: 200,
          success: true,
          message: 'Comments retrieved successfully',
          data: [
            {
              _id: 'comment1',
              blogId: 'blog1',
              name: '<PERSON>',
              email: '<EMAIL>',
              message: 'Great article!',
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
              status: 'approved',
            },
          ],
          pagination: {
            totalItems: 1,
            totalPages: 1,
            currentPage: 1,
            pageSize: 10,
            hasNext: false,
            hasPrev: false,
          },
        },
      };

      const { api } = require('../index');
      api.get.mockResolvedValue(mockResponse);

      const result = await getComments('blog1');

      expect(api.get).toHaveBeenCalledWith('/blogs/blog1/comments', {
        params: { page: 1, limit: 10 },
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle API errors', async () => {
      const { api } = require('../index');
      api.get.mockRejectedValue(new Error('Network error'));

      const result = await getComments('blog1');

      expect(result.success).toBe(false);
      expect(result.message).toBe('Internal Server Error');
    });
  });

  describe('createComment', () => {
    it('should create a comment successfully', async () => {
      const mockResponse = {
        data: {
          status: 201,
          success: true,
          message: 'Comment created successfully',
          data: {
            _id: 'comment1',
            blogId: 'blog1',
            name: 'John Doe',
            email: '<EMAIL>',
            message: 'Great article!',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            status: 'pending',
          },
        },
      };

      const { api } = require('../index');
      api.post.mockResolvedValue(mockResponse);

      const commentData = {
        blogId: 'blog1',
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Great article!',
      };

      const result = await createComment(commentData);

      expect(api.post).toHaveBeenCalledWith('/blogs/blog1/comments', {
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Great article!',
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle validation errors', async () => {
      const mockErrorResponse = {
        response: {
          data: {
            status: 400,
            success: false,
            message: 'Validation error: Name is required',
            data: null,
          },
        },
      };

      const { api } = require('../index');
      api.post.mockRejectedValue(mockErrorResponse);

      const commentData = {
        blogId: 'blog1',
        name: '',
        email: '<EMAIL>',
        message: 'Great article!',
      };

      const result = await createComment(commentData);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Validation error: Name is required');
    });
  });
});

// Example manual testing function
export const testCommentAPI = async () => {
  console.log('Testing Comment API...');
  
  try {
    // Test fetching comments
    console.log('1. Fetching comments...');
    const comments = await getComments('test-blog-id');
    console.log('Comments:', comments);

    // Test creating a comment
    console.log('2. Creating a comment...');
    const newComment = await createComment({
      blogId: 'test-blog-id',
      name: 'Test User',
      email: '<EMAIL>',
      message: 'This is a test comment.',
    });
    console.log('New comment:', newComment);

    console.log('Comment API tests completed!');
  } catch (error) {
    console.error('Comment API test failed:', error);
  }
};
