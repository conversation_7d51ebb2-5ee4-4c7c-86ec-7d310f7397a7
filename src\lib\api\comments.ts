import { type AxiosError } from "axios";
import {
  type CommentsResponse,
  type SingleCommentResponse,
  type CreateCommentData,
} from "@/types/blog";
import { type APIResponse } from "@/types/common";
import { api } from ".";

/**
 * Get comments for a specific blog post
 */
export const getComments = async (blogId: string, page = 1, limit = 10) => {
  try {
    const response = await api.get<APIResponse<CommentsResponse>>(
      `/blogs/${blogId}/comments`,
      {
        params: { page, limit },
      },
    );
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<CommentsResponse>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: [],
        pagination: {
          totalItems: 0,
          totalPages: 0,
          currentPage: 1,
          pageSize: 10,
          hasNext: false,
          hasPrev: false,
        },
      }
    );
  }
};

/**
 * Create a new comment for a blog post
 */
export const createComment = async (commentData: CreateCommentData) => {
  try {
    const response = await api.post<APIResponse<SingleCommentResponse>>(
      `/blogs/${commentData.blogId}/comments`,
      {
        name: commentData.name,
        email: commentData.email,
        message: commentData.message,
      },
    );
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<SingleCommentResponse>>).response
        ?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: null,
      }
    );
  }
};

/**
 * Get a single comment by ID
 */
export const getComment = async (commentId: string) => {
  try {
    const response = await api.get<APIResponse<SingleCommentResponse>>(
      `/comments/${commentId}`,
    );
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<SingleCommentResponse>>).response
        ?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
        data: null,
      }
    );
  }
};

/**
 * Delete a comment (requires authentication)
 */
export const deleteComment = async (commentId: string) => {
  try {
    const response = await api.delete<APIResponse<{ message: string }>>(
      `/comments/${commentId}`,
    );
    return response.data;
  } catch (error) {
    return (
      (error as AxiosError<APIResponse<{ message: string }>>).response?.data || {
        status: 500,
        success: false,
        message: "Internal Server Error",
      }
    );
  }
};
