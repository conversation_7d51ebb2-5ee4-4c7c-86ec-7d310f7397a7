import { z } from "zod";

export const commentSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required.")
    .min(2, "Name must be at least 2 characters.")
    .max(50, "Maximum length of Name is 50 characters."),
  email: z
    .string()
    .min(1, "Email Address is required.")
    .email("Invalid Email Address."),
  message: z
    .string()
    .min(1, "Message is required.")
    .min(10, "Message must be at least 10 characters.")
    .max(1000, "Maximum length of Message is 1000 characters."),
});

export type CommentFormData = z.infer<typeof commentSchema>;
