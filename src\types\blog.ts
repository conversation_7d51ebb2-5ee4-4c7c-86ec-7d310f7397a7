export type Blog = {
  _id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  author: string; // Author name as string
  categories: string[]; // Array of category names
  tags: string[]; // Array of tag names
  imageUrl?: string; // Featured image URL
  publishedAt: string;
  updatedAt: string;
  createdAt?: string;
  status?: "draft" | "published" | "archived";
  readTime?: number;
  viewCount?: number;
  isFeature?: boolean;
  metaTitle?: string;
  metaDescription?: string;
};

export type BlogsResponse = {
  data: Blog[];
  pagination: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

export type SingleBlogResponse = {
  data: Blog;
};

export type BlogsQueryParams = {
  page?: number;
  limit?: number;
  category?: string;
  tag?: string;
  author?: string;
  search?: string;
  status?: "draft" | "published" | "archived";
  sortBy?: "createdAt" | "publishedAt" | "updatedAt" | "title" | "viewCount";
  sortOrder?: "asc" | "desc";
  featured?: boolean;
};

export type CreateBlogData = {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  categoryId?: string;
  tagIds?: string[];
  featuredImage?: string;
  status?: "draft" | "published";
  metaTitle?: string;
  metaDescription?: string;
  isFeature?: boolean;
};

export type UpdateBlogData = Partial<CreateBlogData>;

export type BlogImageUploadResponse = {
  uploadUrl: string;
  imageUrl: string;
  key: string;
};
